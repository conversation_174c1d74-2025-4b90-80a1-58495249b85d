/** This file is auto-generated. Do not modify it manually. */
export type CurrencyInfo = {
  code: string
  name: string
  symbol_native: string
  decimal_digits: number
}

export const currencies: Record<string, CurrencyInfo> = {
  USD: {
    code: "USD",
    name: "US Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  CAD: {
    code: "CAD",
    name: "Canadian Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  EUR: {
    code: "EUR",
    name: "Euro",
    symbol_native: "€",
    decimal_digits: 2,
  },
  AED: {
    code: "AED",
    name: "United Arab Emirates Dirham",
    symbol_native: "د.إ.‏",
    decimal_digits: 2,
  },
  AFN: {
    code: "AFN",
    name: "Afghan Afghani",
    symbol_native: "؋",
    decimal_digits: 0,
  },
  ALL: {
    code: "ALL",
    name: "Albanian Lek",
    symbol_native: "Lek",
    decimal_digits: 0,
  },
  AMD: {
    code: "AMD",
    name: "Armenian Dram",
    symbol_native: "դր.",
    decimal_digits: 0,
  },
  ARS: {
    code: "ARS",
    name: "Argentine Peso",
    symbol_native: "$",
    decimal_digits: 2,
  },
  AUD: {
    code: "AUD",
    name: "Australian Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  AZN: {
    code: "AZN",
    name: "Azerbaijani Manat",
    symbol_native: "ман.",
    decimal_digits: 2,
  },
  BAM: {
    code: "BAM",
    name: "Bosnia-Herzegovina Convertible Mark",
    symbol_native: "KM",
    decimal_digits: 2,
  },
  BDT: {
    code: "BDT",
    name: "Bangladeshi Taka",
    symbol_native: "৳",
    decimal_digits: 2,
  },
  BGN: {
    code: "BGN",
    name: "Bulgarian Lev",
    symbol_native: "лв.",
    decimal_digits: 2,
  },
  BHD: {
    code: "BHD",
    name: "Bahraini Dinar",
    symbol_native: "د.ب.‏",
    decimal_digits: 3,
  },
  BIF: {
    code: "BIF",
    name: "Burundian Franc",
    symbol_native: "FBu",
    decimal_digits: 0,
  },
  BND: {
    code: "BND",
    name: "Brunei Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  BOB: {
    code: "BOB",
    name: "Bolivian Boliviano",
    symbol_native: "Bs",
    decimal_digits: 2,
  },
  BRL: {
    code: "BRL",
    name: "Brazilian Real",
    symbol_native: "R$",
    decimal_digits: 2,
  },
  BWP: {
    code: "BWP",
    name: "Botswanan Pula",
    symbol_native: "P",
    decimal_digits: 2,
  },
  BYN: {
    code: "BYN",
    name: "Belarusian Ruble",
    symbol_native: "руб.",
    decimal_digits: 2,
  },
  BZD: {
    code: "BZD",
    name: "Belize Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  CDF: {
    code: "CDF",
    name: "Congolese Franc",
    symbol_native: "FrCD",
    decimal_digits: 2,
  },
  CHF: {
    code: "CHF",
    name: "Swiss Franc",
    symbol_native: "CHF",
    decimal_digits: 2,
  },
  CLP: {
    code: "CLP",
    name: "Chilean Peso",
    symbol_native: "$",
    decimal_digits: 0,
  },
  CNY: {
    code: "CNY",
    name: "Chinese Yuan",
    symbol_native: "CN¥",
    decimal_digits: 2,
  },
  COP: {
    code: "COP",
    name: "Colombian Peso",
    symbol_native: "$",
    decimal_digits: 0,
  },
  CRC: {
    code: "CRC",
    name: "Costa Rican Colón",
    symbol_native: "₡",
    decimal_digits: 0,
  },
  CVE: {
    code: "CVE",
    name: "Cape Verdean Escudo",
    symbol_native: "CV$",
    decimal_digits: 2,
  },
  CZK: {
    code: "CZK",
    name: "Czech Republic Koruna",
    symbol_native: "Kč",
    decimal_digits: 2,
  },
  DJF: {
    code: "DJF",
    name: "Djiboutian Franc",
    symbol_native: "Fdj",
    decimal_digits: 0,
  },
  DKK: {
    code: "DKK",
    name: "Danish Krone",
    symbol_native: "kr",
    decimal_digits: 2,
  },
  DOP: {
    code: "DOP",
    name: "Dominican Peso",
    symbol_native: "RD$",
    decimal_digits: 2,
  },
  DZD: {
    code: "DZD",
    name: "Algerian Dinar",
    symbol_native: "د.ج.‏",
    decimal_digits: 2,
  },
  EEK: {
    code: "EEK",
    name: "Estonian Kroon",
    symbol_native: "kr",
    decimal_digits: 2,
  },
  EGP: {
    code: "EGP",
    name: "Egyptian Pound",
    symbol_native: "ج.م.‏",
    decimal_digits: 2,
  },
  ERN: {
    code: "ERN",
    name: "Eritrean Nakfa",
    symbol_native: "Nfk",
    decimal_digits: 2,
  },
  ETB: {
    code: "ETB",
    name: "Ethiopian Birr",
    symbol_native: "Br",
    decimal_digits: 2,
  },
  GBP: {
    code: "GBP",
    name: "British Pound Sterling",
    symbol_native: "£",
    decimal_digits: 2,
  },
  GEL: {
    code: "GEL",
    name: "Georgian Lari",
    symbol_native: "GEL",
    decimal_digits: 2,
  },
  GHS: {
    code: "GHS",
    name: "Ghanaian Cedi",
    symbol_native: "GH₵",
    decimal_digits: 2,
  },
  GNF: {
    code: "GNF",
    name: "Guinean Franc",
    symbol_native: "FG",
    decimal_digits: 0,
  },
  GTQ: {
    code: "GTQ",
    name: "Guatemalan Quetzal",
    symbol_native: "Q",
    decimal_digits: 2,
  },
  HKD: {
    code: "HKD",
    name: "Hong Kong Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  HNL: {
    code: "HNL",
    name: "Honduran Lempira",
    symbol_native: "L",
    decimal_digits: 2,
  },
  HRK: {
    code: "HRK",
    name: "Croatian Kuna",
    symbol_native: "kn",
    decimal_digits: 2,
  },
  HUF: {
    code: "HUF",
    name: "Hungarian Forint",
    symbol_native: "Ft",
    decimal_digits: 0,
  },
  IDR: {
    code: "IDR",
    name: "Indonesian Rupiah",
    symbol_native: "Rp",
    decimal_digits: 0,
  },
  ILS: {
    code: "ILS",
    name: "Israeli New Sheqel",
    symbol_native: "₪",
    decimal_digits: 2,
  },
  INR: {
    code: "INR",
    name: "Indian Rupee",
    symbol_native: "₹",
    decimal_digits: 2,
  },
  IQD: {
    code: "IQD",
    name: "Iraqi Dinar",
    symbol_native: "د.ع.‏",
    decimal_digits: 0,
  },
  IRR: {
    code: "IRR",
    name: "Iranian Rial",
    symbol_native: "﷼",
    decimal_digits: 0,
  },
  ISK: {
    code: "ISK",
    name: "Icelandic Króna",
    symbol_native: "kr",
    decimal_digits: 0,
  },
  JMD: {
    code: "JMD",
    name: "Jamaican Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  JOD: {
    code: "JOD",
    name: "Jordanian Dinar",
    symbol_native: "د.أ.‏",
    decimal_digits: 3,
  },
  JPY: {
    code: "JPY",
    name: "Japanese Yen",
    symbol_native: "￥",
    decimal_digits: 0,
  },
  KES: {
    code: "KES",
    name: "Kenyan Shilling",
    symbol_native: "Ksh",
    decimal_digits: 2,
  },
  KHR: {
    code: "KHR",
    name: "Cambodian Riel",
    symbol_native: "៛",
    decimal_digits: 2,
  },
  KMF: {
    code: "KMF",
    name: "Comorian Franc",
    symbol_native: "FC",
    decimal_digits: 0,
  },
  KRW: {
    code: "KRW",
    name: "South Korean Won",
    symbol_native: "₩",
    decimal_digits: 0,
  },
  KWD: {
    code: "KWD",
    name: "Kuwaiti Dinar",
    symbol_native: "د.ك.‏",
    decimal_digits: 3,
  },
  KZT: {
    code: "KZT",
    name: "Kazakhstani Tenge",
    symbol_native: "тңг.",
    decimal_digits: 2,
  },
  LBP: {
    code: "LBP",
    name: "Lebanese Pound",
    symbol_native: "ل.ل.‏",
    decimal_digits: 0,
  },
  LKR: {
    code: "LKR",
    name: "Sri Lankan Rupee",
    symbol_native: "SL Re",
    decimal_digits: 2,
  },
  LTL: {
    code: "LTL",
    name: "Lithuanian Litas",
    symbol_native: "Lt",
    decimal_digits: 2,
  },
  LVL: {
    code: "LVL",
    name: "Latvian Lats",
    symbol_native: "Ls",
    decimal_digits: 2,
  },
  LYD: {
    code: "LYD",
    name: "Libyan Dinar",
    symbol_native: "د.ل.‏",
    decimal_digits: 3,
  },
  MAD: {
    code: "MAD",
    name: "Moroccan Dirham",
    symbol_native: "د.م.‏",
    decimal_digits: 2,
  },
  MDL: {
    code: "MDL",
    name: "Moldovan Leu",
    symbol_native: "MDL",
    decimal_digits: 2,
  },
  MGA: {
    code: "MGA",
    name: "Malagasy Ariary",
    symbol_native: "MGA",
    decimal_digits: 0,
  },
  MKD: {
    code: "MKD",
    name: "Macedonian Denar",
    symbol_native: "MKD",
    decimal_digits: 2,
  },
  MMK: {
    code: "MMK",
    name: "Myanma Kyat",
    symbol_native: "K",
    decimal_digits: 0,
  },
  MNT: {
    code: "MNT",
    name: "Mongolian Tugrig",
    symbol_native: "₮",
    decimal_digits: 0,
  },
  MOP: {
    code: "MOP",
    name: "Macanese Pataca",
    symbol_native: "MOP$",
    decimal_digits: 2,
  },
  MUR: {
    code: "MUR",
    name: "Mauritian Rupee",
    symbol_native: "MURs",
    decimal_digits: 0,
  },
  MXN: {
    code: "MXN",
    name: "Mexican Peso",
    symbol_native: "$",
    decimal_digits: 2,
  },
  MYR: {
    code: "MYR",
    name: "Malaysian Ringgit",
    symbol_native: "RM",
    decimal_digits: 2,
  },
  MZN: {
    code: "MZN",
    name: "Mozambican Metical",
    symbol_native: "MTn",
    decimal_digits: 2,
  },
  NAD: {
    code: "NAD",
    name: "Namibian Dollar",
    symbol_native: "N$",
    decimal_digits: 2,
  },
  NGN: {
    code: "NGN",
    name: "Nigerian Naira",
    symbol_native: "₦",
    decimal_digits: 2,
  },
  NIO: {
    code: "NIO",
    name: "Nicaraguan Córdoba",
    symbol_native: "C$",
    decimal_digits: 2,
  },
  NOK: {
    code: "NOK",
    name: "Norwegian Krone",
    symbol_native: "kr",
    decimal_digits: 2,
  },
  NPR: {
    code: "NPR",
    name: "Nepalese Rupee",
    symbol_native: "नेरू",
    decimal_digits: 2,
  },
  NZD: {
    code: "NZD",
    name: "New Zealand Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  OMR: {
    code: "OMR",
    name: "Omani Rial",
    symbol_native: "ر.ع.‏",
    decimal_digits: 3,
  },
  PAB: {
    code: "PAB",
    name: "Panamanian Balboa",
    symbol_native: "B/.",
    decimal_digits: 2,
  },
  PEN: {
    code: "PEN",
    name: "Peruvian Nuevo Sol",
    symbol_native: "S/.",
    decimal_digits: 2,
  },
  PHP: {
    code: "PHP",
    name: "Philippine Peso",
    symbol_native: "₱",
    decimal_digits: 2,
  },
  PKR: {
    code: "PKR",
    name: "Pakistani Rupee",
    symbol_native: "₨",
    decimal_digits: 0,
  },
  PLN: {
    code: "PLN",
    name: "Polish Zloty",
    symbol_native: "zł",
    decimal_digits: 2,
  },
  PYG: {
    code: "PYG",
    name: "Paraguayan Guarani",
    symbol_native: "₲",
    decimal_digits: 0,
  },
  QAR: {
    code: "QAR",
    name: "Qatari Rial",
    symbol_native: "ر.ق.‏",
    decimal_digits: 2,
  },
  RON: {
    code: "RON",
    name: "Romanian Leu",
    symbol_native: "RON",
    decimal_digits: 2,
  },
  RSD: {
    code: "RSD",
    name: "Serbian Dinar",
    symbol_native: "дин.",
    decimal_digits: 0,
  },
  RUB: {
    code: "RUB",
    name: "Russian Ruble",
    symbol_native: "₽.",
    decimal_digits: 2,
  },
  RWF: {
    code: "RWF",
    name: "Rwandan Franc",
    symbol_native: "FR",
    decimal_digits: 0,
  },
  SAR: {
    code: "SAR",
    name: "Saudi Riyal",
    symbol_native: "ر.س.‏",
    decimal_digits: 2,
  },
  SDG: {
    code: "SDG",
    name: "Sudanese Pound",
    symbol_native: "SDG",
    decimal_digits: 2,
  },
  SEK: {
    code: "SEK",
    name: "Swedish Krona",
    symbol_native: "kr",
    decimal_digits: 2,
  },
  SGD: {
    code: "SGD",
    name: "Singapore Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  SOS: {
    code: "SOS",
    name: "Somali Shilling",
    symbol_native: "Ssh",
    decimal_digits: 0,
  },
  SYP: {
    code: "SYP",
    name: "Syrian Pound",
    symbol_native: "ل.س.‏",
    decimal_digits: 0,
  },
  THB: {
    code: "THB",
    name: "Thai Baht",
    symbol_native: "฿",
    decimal_digits: 2,
  },
  TND: {
    code: "TND",
    name: "Tunisian Dinar",
    symbol_native: "د.ت.‏",
    decimal_digits: 3,
  },
  TOP: {
    code: "TOP",
    name: "Tongan Paʻanga",
    symbol_native: "T$",
    decimal_digits: 2,
  },
  TRY: {
    code: "TRY",
    name: "Turkish Lira",
    symbol_native: "TL",
    decimal_digits: 2,
  },
  TTD: {
    code: "TTD",
    name: "Trinidad and Tobago Dollar",
    symbol_native: "$",
    decimal_digits: 2,
  },
  TWD: {
    code: "TWD",
    name: "New Taiwan Dollar",
    symbol_native: "NT$",
    decimal_digits: 2,
  },
  TZS: {
    code: "TZS",
    name: "Tanzanian Shilling",
    symbol_native: "TSh",
    decimal_digits: 0,
  },
  UAH: {
    code: "UAH",
    name: "Ukrainian Hryvnia",
    symbol_native: "₴",
    decimal_digits: 2,
  },
  UGX: {
    code: "UGX",
    name: "Ugandan Shilling",
    symbol_native: "USh",
    decimal_digits: 0,
  },
  UYU: {
    code: "UYU",
    name: "Uruguayan Peso",
    symbol_native: "$",
    decimal_digits: 2,
  },
  UZS: {
    code: "UZS",
    name: "Uzbekistan Som",
    symbol_native: "UZS",
    decimal_digits: 0,
  },
  VEF: {
    code: "VEF",
    name: "Venezuelan Bolívar",
    symbol_native: "Bs.F.",
    decimal_digits: 2,
  },
  VND: {
    code: "VND",
    name: "Vietnamese Dong",
    symbol_native: "₫",
    decimal_digits: 0,
  },
  XAF: {
    code: "XAF",
    name: "CFA Franc BEAC",
    symbol_native: "FCFA",
    decimal_digits: 0,
  },
  XOF: {
    code: "XOF",
    name: "CFA Franc BCEAO",
    symbol_native: "CFA",
    decimal_digits: 0,
  },
  YER: {
    code: "YER",
    name: "Yemeni Rial",
    symbol_native: "ر.ي.‏",
    decimal_digits: 0,
  },
  ZAR: {
    code: "ZAR",
    name: "South African Rand",
    symbol_native: "R",
    decimal_digits: 2,
  },
  ZMK: {
    code: "ZMK",
    name: "Zambian Kwacha",
    symbol_native: "ZK",
    decimal_digits: 0,
  },
  ZWL: {
    code: "ZWL",
    name: "Zimbabwean Dollar",
    symbol_native: "ZWL$",
    decimal_digits: 0,
  },
}

export function getCurrencySymbol(code: string) {
  return currencies[code.toUpperCase()].symbol_native
}
