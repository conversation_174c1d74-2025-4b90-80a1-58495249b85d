import { AuthenticatedMedusaRequest, MedusaResponse } from '@medusajs/framework'
import { ContainerRegistrationKeys } from '@medusajs/framework/utils'

import { updateMemberWorkflow } from '../../../../workflows/member/workflows'
import { deleteMemberWorkflow } from '../../../../workflows/member/workflows/delete-member'
import { VendorUpdateMemberType } from '../validators'

/**
 * @oas [post] /vendor/members/{id}
 * operationId: "VendorUpdateMemberById"
 * summary: "Update a Member"
 * description: "Updates a member by id for the authenticated vendor."
 * x-authenticated: true
 * parameters:
 *   - in: path
 *     name: id
 *     required: true
 *     description: The ID of the Member.
 *     schema:
 *       type: string
 * requestBody:
 *   content:
 *     application/json:
 *       schema:
 *         $ref: "#/components/schemas/VendorUpdateMember"
 * responses:
 *   "200":
 *     description: OK
 *     content:
 *       application/json:
 *         schema:
 *           type: object
 *           properties:
 *             member:
 *               $ref: "#/components/schemas/VendorMember"
 * tags:
 *   - Vendor Members
 * security:
 *   - api_token: []
 *   - cookie_auth: []
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<VendorUpdateMemberType>,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  const { id } = req.params

  await updateMemberWorkflow(req.scope).run({
    input: {
      id,
      ...req.validatedBody
    }
  })

  const {
    data: [member]
  } = await query.graph(
    {
      entity: 'member',
      fields: req.queryConfig.fields,
      filters: { id: id }
    },
    { throwIfKeyNotFound: true }
  )

  res.json({ member })
}

/**
 * @oas [get] /vendor/members/{id}
 * operationId: "VendorGetMemberById"
 * summary: "Get a Member"
 * description: "Retrieves a member by id."
 * x-authenticated: true
 * parameters:
 *   - in: path
 *     name: id
 *     required: true
 *     description: The ID of the Member.
 *     schema:
 *       type: string
 * responses:
 *   "200":
 *     description: OK
 *     content:
 *       application/json:
 *         schema:
 *           type: object
 *           properties:
 *             member:
 *               $ref: "#/components/schemas/VendorMember"
 * tags:
 *   - Vendor Members
 * security:
 *   - api_token: []
 *   - cookie_auth: []
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)

  const { id } = req.params
  const {
    data: [member]
  } = await query.graph(
    {
      entity: 'member',
      fields: req.queryConfig.fields,
      filters: { id: id }
    },
    { throwIfKeyNotFound: true }
  )

  res.json({ member })
}

/**
 * @oas [delete] /vendor/members/{id}
 * operationId: "VendorDeleteMemberById"
 * summary: "Delete a Member"
 * description: "Deletes a member by id."
 * x-authenticated: true
 * parameters:
 *   - in: path
 *     name: id
 *     required: true
 *     description: The ID of the Member.
 *     schema:
 *       type: string
 * responses:
 *   "200":
 *     description: OK
 *     content:
 *       application/json:
 *         schema:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *               description: The ID of the deleted Member
 *             object:
 *               type: string
 *               description: The type of the object that was deleted
 *             deleted:
 *               type: boolean
 *               description: Whether or not the items were deleted
 * tags:
 *   - Vendor Members
 * security:
 *   - api_token: []
 *   - cookie_auth: []
 */
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const { id } = req.params

  await deleteMemberWorkflow(req.scope).run({
    input: id
  })

  res.json({
    id,
    object: 'member',
    deleted: true
  })
}
