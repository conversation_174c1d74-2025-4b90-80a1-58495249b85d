export const adminOrderSetFields = [
  'id',
  'display_id',
  'sales_channel_id',
  '*sales_channel',
  'cart_id',
  '*cart',
  'customer_id',
  '*customer',
  'payment_collection_id',
  '*payment_collection',
  'orders.id',
  'orders.display_id',
  'orders.region_id',
  'orders.status',
  'orders.version',
  'orders.summary',
  'orders.total',
  'orders.subtotal',
  'orders.tax_total',
  'orders.order_change',
  'orders.discount_total',
  'orders.discount_tax_total',
  'orders.original_total',
  'orders.original_tax_total',
  'orders.item_total',
  'orders.item_subtotal',
  'orders.item_tax_total',
  'orders.original_item_total',
  'orders.original_item_subtotal',
  'orders.original_item_tax_total',
  'orders.shipping_total',
  'orders.shipping_subtotal',
  'orders.shipping_tax_total',
  'orders.original_shipping_tax_total',
  'orders.original_shipping_subtotal',
  'orders.original_shipping_total',
  'orders.created_at',
  'orders.updated_at',
  'orders.items.*',
  'orders.items.tax_lines.*',
  'orders.items.adjustments.*',
  'orders.items.variant.*',
  'orders.items.variant.product.*',
  'orders.items.detail.*',
  'orders.shipping_address.*',
  'orders.billing_address.*',
  'orders.shipping_methods.*',
  'orders.shipping_methods.tax_lines.*',
  'orders.shipping_methods.adjustments.*',
  'orders.payment_collections.*',
  'orders.payment_collections.payments.*',
  'orders.payment_collections.payments.refunds.*'
]

export const adminOrderSetQueryConfig = {
  list: {
    defaults: adminOrderSetFields,
    isList: true
  },
  retrieve: {
    defaults: adminOrderSetFields,
    isList: false
  }
}
