import { MiddlewareRoute, validateAndTransformQuery } from "@medusajs/framework"
import { StoreGetProductsParams } from "@medusajs/medusa/api/store/products/validators"
import { storeProductQueryConfig } from "./query-config"

export const storeProductsMiddlewares: MiddlewareRoute[] = [
  {
    method: ["GET"],
    matcher: "/store/products",
    middlewares: [
      validateAndTransformQuery(
        StoreGetProductsParams,
        storeProductQueryConfig.list
      ),
    ],
  },
]
