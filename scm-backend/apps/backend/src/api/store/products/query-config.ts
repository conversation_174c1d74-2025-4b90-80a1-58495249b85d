export const storeProductQueryConfig = {
  list: {
    defaults: [
      "id",
      "title",
      "handle",
      "thumbnail",
      "status",
      "created_at",
      "updated_at",
      "*variants",
      "*variants.prices",
      "*variants.inventory_quantity",
      "*seller",
      "*seller.products",
      "*seller.reviews",
      "*seller.reviews.customer",
      "*seller.reviews.seller",
      "*seller.products.variants",
      "*attribute_values",
      "*attribute_values.attribute",
    ],
    allowed: [
      "id",
      "title",
      "handle",
      "thumbnail",
      "status",
      "created_at",
      "updated_at",
      "variants",
      "variants.id",
      "variants.title",
      "variants.sku",
      "variants.prices",
      "variants.prices.amount",
      "variants.prices.currency_code",
      "variants.calculated_price",
      "variants.inventory_quantity",
      "seller",
      "seller.id",
      "seller.name",
      "seller.handle",
      "seller.products",
      "seller.reviews",
      "seller.reviews.customer",
      "seller.reviews.seller",
      "seller.products.variants",
      "attribute_values",
      "attribute_values.attribute",
    ],
  },
}
