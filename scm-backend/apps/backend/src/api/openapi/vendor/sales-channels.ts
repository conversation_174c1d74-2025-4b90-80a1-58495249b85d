/**
 * @schema VendorSalesChannel
 * type: object
 * description: The details of the sales channel.
 * x-schemaName: VendorSalesChannel
 * properties:
 *   id:
 *     type: string
 *     title: id
 *     description: The sales channel ID.
 *   name:
 *     type: string
 *     title: name
 *     description: The sales channel name.
 *   description:
 *     type: string
 *     title: description
 *     description: The sales channel description.
 *   is_disabled:
 *     type: boolean
 *     title: is_disabled
 *     description: Is sales channel disabled.
 *   metadata:
 *     type: object
 *     title: metadata
 *     description: The sales channel metadata.
 *   created_at:
 *     type: string
 *     format: date-time
 *     title: created_at
 *     description: The date the channel was created.
 *   updated_at:
 *     type: string
 *     format: date-time
 *     title: updated_at
 *     description: The date the channel was updated.
 *   deleted_at:
 *     type: string
 *     format: date-time
 *     title: deleted_at
 *     description: The date the channel was deleted.
 *
 */
